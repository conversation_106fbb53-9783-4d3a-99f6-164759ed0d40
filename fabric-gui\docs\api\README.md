# Fabric REST API Documentation

This document describes the REST API endpoints that the Fabric Pattern Runner GUI uses to communicate with the Fabric backend.

## Base URL

```
http://localhost:8080
```

## Authentication

The API supports optional authentication via API key. If configured, include the API key in the Authorization header:

```
Authorization: Bearer your-api-key-here
```

## Endpoints

### Health Check

Check if the API server is running and responsive.

```http
GET /health
```

**Response:**
```
HTTP/200 OK
Content-Type: text/plain

OK
```

### List Patterns

Get all available Fabric patterns.

```http
GET /patterns
```

**Response:**
```json
HTTP/200 OK
Content-Type: application/json

[
  "summarize",
  "extract_wisdom",
  "analyze_paper",
  "create_summary",
  "explain_code"
]
```

### List Models

Get all available AI models.

```http
GET /models
```

**Response:**
```json
HTTP/200 OK
Content-Type: application/json

[
  {
    "name": "gpt-4",
    "provider": "OpenAI"
  },
  {
    "name": "claude-3-sonnet",
    "provider": "Anthropic"
  }
]
```

### Execute Pattern

Execute a Fabric pattern with the provided input text.

```http
POST /patterns/{pattern_name}
Content-Type: text/plain

Input text to process...
```

**Parameters:**
- `pattern_name` (path): Name of the pattern to execute
- Body: Plain text input to process

**Query Parameters:**
- `model` (optional): Specific model to use
- `temperature` (optional): Temperature setting (0.0-2.0)
- `stream` (optional): Enable streaming response

**Example:**
```http
POST /patterns/summarize?temperature=0.7
Content-Type: text/plain

Artificial intelligence is rapidly transforming industries across the globe. From healthcare to finance, AI technologies are enabling new capabilities and improving efficiency. However, this transformation also brings challenges including ethical considerations, job displacement, and the need for new regulatory frameworks. Organizations must carefully balance innovation with responsibility as they implement AI solutions.
```

**Response:**
```
HTTP/200 OK
Content-Type: text/plain

AI is transforming industries globally, bringing both opportunities for improved efficiency and challenges including ethics, job displacement, and regulatory needs. Organizations must balance innovation with responsibility.
```

### Execute Pattern (Streaming)

Execute a pattern with streaming response for real-time output.

```http
POST /patterns/{pattern_name}?stream=true
Content-Type: text/plain

Input text to process...
```

**Response:**
```
HTTP/200 OK
Content-Type: text/plain
Transfer-Encoding: chunked

[Streaming response chunks...]
```

## Error Responses

### 400 Bad Request
```json
{
  "error": "Invalid request",
  "message": "Pattern name is required"
}
```

### 401 Unauthorized
```json
{
  "error": "Unauthorized",
  "message": "Invalid or missing API key"
}
```

### 404 Not Found
```json
{
  "error": "Pattern not found",
  "message": "Pattern 'invalid_pattern' does not exist"
}
```

### 500 Internal Server Error
```json
{
  "error": "Execution failed",
  "message": "Model timeout or other processing error"
}
```

## Rate Limiting

The API may implement rate limiting based on:
- Requests per minute per IP
- Concurrent executions per user
- Total processing time per hour

Rate limit headers are included in responses:
```
X-RateLimit-Limit: 100
X-RateLimit-Remaining: 95
X-RateLimit-Reset: 1640995200
```

## CORS Support

For web GUI compatibility, the API supports Cross-Origin Resource Sharing (CORS):

```
Access-Control-Allow-Origin: *
Access-Control-Allow-Methods: GET, POST, OPTIONS
Access-Control-Allow-Headers: Content-Type, Authorization
```

## WebSocket Support (Future)

Future versions may support WebSocket connections for real-time pattern execution:

```
ws://localhost:8080/ws/patterns/{pattern_name}
```

## SDK Examples

### JavaScript/Browser
```javascript
// Execute pattern
const response = await fetch('http://localhost:8080/patterns/summarize', {
  method: 'POST',
  headers: {
    'Content-Type': 'text/plain',
    'Authorization': 'Bearer your-api-key'
  },
  body: 'Your input text here...'
});

const result = await response.text();
console.log(result);
```

### Python
```python
import requests

# Execute pattern
response = requests.post(
    'http://localhost:8080/patterns/summarize',
    headers={
        'Content-Type': 'text/plain',
        'Authorization': 'Bearer your-api-key'
    },
    data='Your input text here...'
)

print(response.text)
```

### cURL
```bash
# Execute pattern
curl -X POST \
  -H "Content-Type: text/plain" \
  -H "Authorization: Bearer your-api-key" \
  -d "Your input text here..." \
  http://localhost:8080/patterns/summarize
```

## Configuration

### Server Configuration
The Fabric server can be configured with these options:

```bash
# Start server with custom configuration
fabric --serve \
  --address localhost:8080 \
  --cors-allow-origin "http://localhost:3000" \
  --api-key "your-secret-key" \
  --timeout 60
```

### Environment Variables
```bash
export FABRIC_API_HOST=localhost
export FABRIC_API_PORT=8080
export FABRIC_API_KEY=your-secret-key
export FABRIC_CORS_ORIGINS="http://localhost:3000,https://yourdomain.com"
```

## Monitoring

### Health Monitoring
Regular health checks should be performed:

```bash
# Simple health check
curl http://localhost:8080/health

# Detailed status (if available)
curl http://localhost:8080/status
```

### Logging
Server logs include:
- Request timestamps and durations
- Pattern execution details
- Error messages and stack traces
- Performance metrics

### Metrics
The API may expose metrics endpoints:

```http
GET /metrics
```

Response includes:
- Request counts by pattern
- Average execution times
- Error rates
- System resource usage

## Security Considerations

### Input Validation
- All input text is sanitized
- Pattern names are validated against allowed list
- Request size limits are enforced

### Output Sanitization
- Model outputs are filtered for sensitive content
- No user data is logged in production

### Network Security
- HTTPS support in production
- API key encryption in transit
- Rate limiting and DDoS protection

## Troubleshooting

### Common Issues

#### Connection Refused
```
Error: connect ECONNREFUSED localhost:8080
```
- **Solution**: Ensure Fabric server is running

#### CORS Errors
```
Access to fetch at 'http://localhost:8080/patterns/summarize' 
from origin 'http://localhost:3000' has been blocked by CORS policy
```
- **Solution**: Start server with CORS configuration

#### Authentication Errors
```
{"error": "Unauthorized", "message": "Invalid API key"}
```
- **Solution**: Check API key configuration

### Debug Mode
Enable debug logging:

```bash
export FABRIC_DEBUG=true
fabric --serve --debug
```

---

For more information, see the [Fabric documentation](https://github.com/danielmiessler/fabric).
