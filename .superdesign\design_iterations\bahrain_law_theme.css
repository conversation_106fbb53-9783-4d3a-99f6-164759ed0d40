:root {
  --background: oklch(1.0000 0 0);
  --foreground: oklch(0.1450 0 0);
  --card: oklch(1.0000 0 0);
  --card-foreground: oklch(0.1450 0 0);
  --popover: oklch(1.0000 0 0);
  --popover-foreground: oklch(0.1450 0 0);
  --primary: #1a3a6d; /* Deep blue for trust */
  --primary-foreground: oklch(0.9850 0 0);
  --secondary: #d4af37; /* Gold for Bahrain cultural elements */
  --secondary-foreground: oklch(0.1450 0 0);
  --muted: oklch(0.9500 0 0);
  --muted-foreground: oklch(0.5560 0 0);
  --accent: #2c5282; /* Slightly lighter blue */
  --accent-foreground: oklch(0.9850 0 0);
  --destructive: #e53e3e; /* Error/red color */
  --destructive-foreground: oklch(1 0 0);
  --border: oklch(0.9220 0 0);
  --input: oklch(0.9220 0 0);
  --ring: #1a3a6d;
  --chart-1: #1a3a6d;
  --chart-2: #2c5282;
  --chart-3: #d4af37;
  --chart-4: #81c784;
  --chart-5: #4caf50;
  --sidebar: oklch(0.9850 0 0);
  --sidebar-foreground: oklch(0.1450 0 0);
  --sidebar-primary: #1a3a6d;
  --sidebar-primary-foreground: oklch(0.9850 0 0);
  --sidebar-accent: #2c5282;
  --sidebar-accent-foreground: oklch(0.9850 0 0);
  --sidebar-border: oklch(0.9220 0 0);
  --sidebar-ring: #1a3a6d;
  --font-sans: 'Tajawal', 'Segoe UI', sans-serif;
  --font-serif: 'Amiri', Georgia, serif;
  --font-mono: 'Roboto Mono', monospace;
  --radius: 0.5rem;
  --shadow-2xs: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-xs: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px -1px rgba(0, 0, 0, 0.1);
  --shadow-sm: 0 2px 4px 0 rgba(0, 0, 0, 0.1), 0 1px 2px -1px rgba(0, 0, 0, 0.1);
  --shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -2px rgba(0, 0, 0, 0.1);
  --shadow-md: 0 8px 12px -4px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-lg: 0 12px 16px -4px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 8px 10px -6px rgba(0, 0, 0, 0.1);
  --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  --tracking-normal: 0em;
  --spacing: 0.25rem;

  --radius-sm: calc(var(--radius) - 0.125rem);
  --radius-md: calc(var(--radius) - 0.0625rem);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 0.125rem);
}