{"version": 3, "file": "format.js", "sourceRoot": "", "sources": ["../../../lib/vocabularies/format/format.ts"], "names": [], "mappings": ";;AASA,mDAAoF;AAapF,MAAM,KAAK,GAA2B;IACpC,OAAO,EAAE,CAAC,EAAC,UAAU,EAAC,EAAE,EAAE,CAAC,IAAA,aAAG,EAAA,sBAAsB,UAAU,GAAG;IACjE,MAAM,EAAE,CAAC,EAAC,UAAU,EAAC,EAAE,EAAE,CAAC,IAAA,WAAC,EAAA,YAAY,UAAU,GAAG;CACrD,CAAA;AAED,MAAM,GAAG,GAA0B;IACjC,OAAO,EAAE,QAAQ;IACjB,IAAI,EAAE,CAAC,QAAQ,EAAE,QAAQ,CAAC;IAC1B,UAAU,EAAE,QAAQ;IACpB,KAAK,EAAE,IAAI;IACX,KAAK;IACL,IAAI,CAAC,GAAe,EAAE,QAAiB;QACrC,MAAM,EAAC,GAAG,EAAE,IAAI,EAAE,KAAK,EAAE,MAAM,EAAE,UAAU,EAAE,EAAE,EAAC,GAAG,GAAG,CAAA;QACtD,MAAM,EAAC,IAAI,EAAE,aAAa,EAAE,SAAS,EAAE,IAAI,EAAC,GAAG,EAAE,CAAA;QACjD,IAAI,CAAC,IAAI,CAAC,eAAe;YAAE,OAAM;QAEjC,IAAI,KAAK;YAAE,mBAAmB,EAAE,CAAA;;YAC3B,cAAc,EAAE,CAAA;QAErB,SAAS,mBAAmB;YAC1B,MAAM,IAAI,GAAG,GAAG,CAAC,UAAU,CAAC,SAAS,EAAE;gBACrC,GAAG,EAAE,IAAI,CAAC,OAAO;gBACjB,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,OAAO;aACxB,CAAC,CAAA;YACF,MAAM,IAAI,GAAG,GAAG,CAAC,KAAK,CAAC,MAAM,EAAE,IAAA,WAAC,EAAA,GAAG,IAAI,IAAI,UAAU,GAAG,CAAC,CAAA;YACzD,MAAM,KAAK,GAAG,GAAG,CAAC,GAAG,CAAC,OAAO,CAAC,CAAA;YAC9B,MAAM,MAAM,GAAG,GAAG,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAA;YAChC,gBAAgB;YAChB,GAAG,CAAC,EAAE,CACJ,IAAA,WAAC,EAAA,UAAU,IAAI,qBAAqB,IAAI,qBAAqB,EAC7D,GAAG,EAAE,CAAC,GAAG,CAAC,MAAM,CAAC,KAAK,EAAE,IAAA,WAAC,EAAA,GAAG,IAAI,mBAAmB,CAAC,CAAC,MAAM,CAAC,MAAM,EAAE,IAAA,WAAC,EAAA,GAAG,IAAI,WAAW,CAAC,EACxF,GAAG,EAAE,CAAC,GAAG,CAAC,MAAM,CAAC,KAAK,EAAE,IAAA,WAAC,EAAA,UAAU,CAAC,CAAC,MAAM,CAAC,MAAM,EAAE,IAAI,CAAC,CAC1D,CAAA;YACD,GAAG,CAAC,SAAS,CAAC,IAAA,YAAE,EAAC,UAAU,EAAE,EAAE,UAAU,EAAE,CAAC,CAAC,CAAA;YAE7C,SAAS,UAAU;gBACjB,IAAI,IAAI,CAAC,YAAY,KAAK,KAAK;oBAAE,OAAO,aAAG,CAAA;gBAC3C,OAAO,IAAA,WAAC,EAAA,GAAG,UAAU,QAAQ,MAAM,EAAE,CAAA;YACvC,CAAC;YAED,SAAS,UAAU;gBACjB,MAAM,UAAU,GAAG,SAAS,CAAC,MAAM;oBACjC,CAAC,CAAC,IAAA,WAAC,EAAA,IAAI,IAAI,kBAAkB,MAAM,IAAI,IAAI,OAAO,MAAM,IAAI,IAAI,IAAI;oBACpE,CAAC,CAAC,IAAA,WAAC,EAAA,GAAG,MAAM,IAAI,IAAI,GAAG,CAAA;gBACzB,MAAM,SAAS,GAAG,IAAA,WAAC,EAAA,WAAW,MAAM,oBAAoB,UAAU,MAAM,MAAM,SAAS,IAAI,IAAI,CAAA;gBAC/F,OAAO,IAAA,WAAC,EAAA,GAAG,MAAM,OAAO,MAAM,gBAAgB,KAAK,QAAQ,QAAQ,QAAQ,SAAS,EAAE,CAAA;YACxF,CAAC;QACH,CAAC;QAED,SAAS,cAAc;YACrB,MAAM,SAAS,GAA4B,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAA;YAC/D,IAAI,CAAC,SAAS,EAAE,CAAC;gBACf,aAAa,EAAE,CAAA;gBACf,OAAM;YACR,CAAC;YACD,IAAI,SAAS,KAAK,IAAI;gBAAE,OAAM;YAC9B,MAAM,CAAC,OAAO,EAAE,MAAM,EAAE,MAAM,CAAC,GAAG,SAAS,CAAC,SAAS,CAAC,CAAA;YACtD,IAAI,OAAO,KAAK,QAAQ;gBAAE,GAAG,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,CAAA;YAEpD,SAAS,aAAa;gBACpB,IAAI,IAAI,CAAC,YAAY,KAAK,KAAK,EAAE,CAAC;oBAChC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC,CAAA;oBAC9B,OAAM;gBACR,CAAC;gBACD,MAAM,IAAI,KAAK,CAAC,UAAU,EAAE,CAAC,CAAA;gBAE7B,SAAS,UAAU;oBACjB,OAAO,mBAAmB,MAAgB,gCAAgC,aAAa,GAAG,CAAA;gBAC5F,CAAC;YACH,CAAC;YAED,SAAS,SAAS,CAAC,MAAmB;gBACpC,MAAM,IAAI,GACR,MAAM,YAAY,MAAM;oBACtB,CAAC,CAAC,IAAA,oBAAU,EAAC,MAAM,CAAC;oBACpB,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO;wBACnB,CAAC,CAAC,IAAA,WAAC,EAAA,GAAG,IAAI,CAAC,IAAI,CAAC,OAAO,GAAG,IAAA,qBAAW,EAAC,MAAM,CAAC,EAAE;wBAC/C,CAAC,CAAC,SAAS,CAAA;gBACf,MAAM,GAAG,GAAG,GAAG,CAAC,UAAU,CAAC,SAAS,EAAE,EAAC,GAAG,EAAE,MAAM,EAAE,GAAG,EAAE,MAAM,EAAE,IAAI,EAAC,CAAC,CAAA;gBACvE,IAAI,OAAO,MAAM,IAAI,QAAQ,IAAI,CAAC,CAAC,MAAM,YAAY,MAAM,CAAC,EAAE,CAAC;oBAC7D,OAAO,CAAC,MAAM,CAAC,IAAI,IAAI,QAAQ,EAAE,MAAM,CAAC,QAAQ,EAAE,IAAA,WAAC,EAAA,GAAG,GAAG,WAAW,CAAC,CAAA;gBACvE,CAAC;gBAED,OAAO,CAAC,QAAQ,EAAE,MAAM,EAAE,GAAG,CAAC,CAAA;YAChC,CAAC;YAED,SAAS,cAAc;gBACrB,IAAI,OAAO,SAAS,IAAI,QAAQ,IAAI,CAAC,CAAC,SAAS,YAAY,MAAM,CAAC,IAAI,SAAS,CAAC,KAAK,EAAE,CAAC;oBACtF,IAAI,CAAC,SAAS,CAAC,MAAM;wBAAE,MAAM,IAAI,KAAK,CAAC,6BAA6B,CAAC,CAAA;oBACrE,OAAO,IAAA,WAAC,EAAA,SAAS,MAAM,IAAI,IAAI,GAAG,CAAA;gBACpC,CAAC;gBACD,OAAO,OAAO,MAAM,IAAI,UAAU,CAAC,CAAC,CAAC,IAAA,WAAC,EAAA,GAAG,MAAM,IAAI,IAAI,GAAG,CAAC,CAAC,CAAC,IAAA,WAAC,EAAA,GAAG,MAAM,SAAS,IAAI,GAAG,CAAA;YACzF,CAAC;QACH,CAAC;IACH,CAAC;CACF,CAAA;AAED,kBAAe,GAAG,CAAA"}