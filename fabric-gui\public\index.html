<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Modern Fabric AI Pattern Runner with Glass-morphism Design">
    <title><PERSON><PERSON><PERSON> Runner - Modern UI</title>

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=JetBrains+Mono:wght@400;500&display=swap" rel="stylesheet">

    <!-- Icons - Lucide for modern crisp icons -->
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>

    <style>
        /* ============================================
           MODERN DESIGN SYSTEM - NEON GLASS THEME
           ============================================ */

        :root {
            /* Neon Accent Colors */
            --neon-primary: #00D4FF;
            --neon-secondary: #FF006E;
            --neon-tertiary: #FFBE0B;
            --neon-glow: rgba(0, 212, 255, 0.5);
            --neon-glow-secondary: rgba(255, 0, 110, 0.3);

            /* Glass-morphism Variables */
            --glass-bg: rgba(255, 255, 255, 0.1);
            --glass-bg-strong: rgba(255, 255, 255, 0.15);
            --glass-border: rgba(255, 255, 255, 0.2);
            --glass-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);

            /* Dark Theme Glass */
            --glass-bg-dark: rgba(20, 20, 30, 0.8);
            --glass-bg-dark-strong: rgba(30, 30, 50, 0.9);
            --glass-border-dark: rgba(255, 255, 255, 0.1);

            /* Light Theme Glass */
            --glass-bg-light: rgba(255, 255, 255, 0.7);
            --glass-bg-light-strong: rgba(255, 255, 255, 0.9);
            --glass-border-light: rgba(0, 0, 0, 0.1);

            /* Typography */
            --font-sans: 'Inter', system-ui, sans-serif;
            --font-mono: 'JetBrains Mono', 'Fira Code', monospace;

            /* Spacing Scale */
            --space-xs: 0.25rem;
            --space-sm: 0.5rem;
            --space-md: 1rem;
            --space-lg: 1.5rem;
            --space-xl: 2rem;
            --space-2xl: 3rem;

            /* Border Radius */
            --radius-sm: 0.5rem;
            --radius-md: 0.75rem;
            --radius-lg: 1rem;
            --radius-xl: 1.5rem;

            /* Animations */
            --transition-fast: 200ms ease-out;
            --transition-normal: 300ms ease-out;
            --transition-slow: 500ms ease-out;
        }

        /* Dark Theme Variables */
        .theme-dark {
            --text-primary: #ffffff;
            --text-secondary: #b0b0b0;
            --text-tertiary: #808080;
            --bg-primary: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%);
            --bg-card: var(--glass-bg-dark);
            --bg-card-hover: var(--glass-bg-dark-strong);
            --border-color: var(--glass-border-dark);
        }

        /* Light Theme Variables */
        .theme-light {
            --text-primary: #1a1a1a;
            --text-secondary: #666666;
            --text-tertiary: #999999;
            --bg-primary: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
            --bg-card: rgba(255, 255, 255, 0.7);
            --bg-card-hover: rgba(255, 255, 255, 0.9);
            --border-color: rgba(0, 0, 0, 0.1);
        }

        /* ============================================
           RESET & BASE STYLES
           ============================================ */

        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }

        html {
            font-size: 16px;
            scroll-behavior: smooth;
        }

        body {
            font-family: var(--font-sans);
            color: var(--text-primary);
            background: var(--bg-primary);
            background-attachment: fixed;
            min-height: 100vh;
            transition: all var(--transition-normal);
            overflow-x: hidden;
        }

        /* Glass Morphism Card Base */
        .glass-card {
            background: var(--bg-card);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            border: 1px solid var(--border-color);
            border-radius: var(--radius-lg);
            box-shadow: var(--glass-shadow);
            transition: all var(--transition-normal);
        }

        .glass-card:hover {
            background: var(--bg-card-hover);
            transform: translateY(-2px);
            box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
        }

        /* Neon Glow Effects */
        .neon-glow {
            box-shadow: 0 0 20px var(--neon-glow),
                        0 0 40px var(--neon-glow),
                        0 0 60px var(--neon-glow);
        }

        .neon-border {
            border: 1px solid var(--neon-primary);
            box-shadow: 0 0 10px var(--neon-glow);
        }

        /* ============================================
           LAYOUT & STRUCTURE
           ============================================ */

        .app-container {
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }

        .header {
            background: var(--bg-card);
            backdrop-filter: blur(20px);
            border-bottom: 1px solid var(--border-color);
            padding: var(--space-md) var(--space-xl);
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .header-content {
            display: flex;
            align-items: center;
            justify-content: space-between;
            max-width: 1400px;
            margin: 0 auto;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: var(--space-md);
        }

        .logo-icon {
            width: 40px;
            height: 40px;
            background: linear-gradient(135deg, var(--neon-primary), var(--neon-secondary));
            border-radius: var(--radius-sm);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
        }

        .logo h1 {
            font-size: 1.5rem;
            font-weight: 600;
            background: linear-gradient(135deg, var(--neon-primary), var(--neon-secondary));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .header-controls {
            display: flex;
            align-items: center;
            gap: var(--space-sm);
        }

        .main-content {
            flex: 1;
            padding: var(--space-xl);
            max-width: 1400px;
            margin: 0 auto;
            width: 100%;
        }

        .content-grid {
            display: grid;
            grid-template-columns: 350px 1fr;
            gap: var(--space-xl);
            min-height: calc(100vh - 120px);
        }

        .sidebar {
            display: flex;
            flex-direction: column;
            gap: var(--space-lg);
        }

        .main-panel {
            display: flex;
            flex-direction: column;
            gap: var(--space-lg);
        }

        /* ============================================
           COMPONENTS
           ============================================ */

        /* Buttons */
        .btn {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: var(--space-sm);
            padding: var(--space-sm) var(--space-md);
            border: none;
            border-radius: var(--radius-md);
            font-family: inherit;
            font-size: 0.9rem;
            font-weight: 500;
            text-decoration: none;
            cursor: pointer;
            transition: all var(--transition-fast);
            position: relative;
            overflow: hidden;
        }

        .btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--neon-primary), var(--neon-secondary));
            color: white;
            font-weight: 600;
        }

        .btn-primary:not(:disabled):hover {
            transform: scale(1.05);
            box-shadow: 0 0 20px var(--neon-glow);
        }

        .btn-secondary {
            background: var(--bg-card);
            color: var(--text-primary);
            border: 1px solid var(--border-color);
        }

        .btn-secondary:not(:disabled):hover {
            background: var(--bg-card-hover);
            transform: translateY(-2px);
        }

        .btn-ghost {
            background: transparent;
            color: var(--text-secondary);
            border: 1px solid transparent;
        }

        .btn-ghost:not(:disabled):hover {
            background: var(--bg-card);
            color: var(--text-primary);
        }

        .btn-large {
            padding: var(--space-md) var(--space-xl);
            font-size: 1.1rem;
        }

        .btn-icon {
            width: 40px;
            height: 40px;
            padding: 0;
            border-radius: 50%;
        }

        /* Form Elements */
        .form-group {
            display: flex;
            flex-direction: column;
            gap: var(--space-xs);
        }

        .form-label {
            font-size: 0.9rem;
            font-weight: 500;
            color: var(--text-secondary);
        }

        .form-control {
            padding: var(--space-md);
            background: var(--bg-card);
            border: 1px solid var(--border-color);
            border-radius: var(--radius-md);
            color: var(--text-primary);
            font-family: inherit;
            transition: all var(--transition-fast);
        }

        .form-control:focus {
            outline: none;
            border-color: var(--neon-primary);
            box-shadow: 0 0 0 2px var(--neon-glow);
        }

        .form-control.mono {
            font-family: var(--font-mono);
            font-size: 0.9rem;
        }

        .textarea-large {
            min-height: 200px;
            resize: vertical;
        }

        select.form-control {
            cursor: pointer;
        }

        /* Cards */
        .card {
            padding: var(--space-xl);
        }

        .card-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: var(--space-lg);
            padding-bottom: var(--space-md);
            border-bottom: 1px solid var(--border-color);
        }

        .card-title {
            display: flex;
            align-items: center;
            gap: var(--space-sm);
            font-size: 1.1rem;
            font-weight: 600;
            color: var(--text-primary);
        }

        .card-title i {
            color: var(--neon-primary);
        }

        /* Status Indicators */
        .status-indicator {
            display: flex;
            align-items: center;
            gap: var(--space-sm);
            font-size: 0.9rem;
        }

        .status-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            animation: pulse 2s infinite;
        }

        .status-connected { color: #10B981; }
        .status-connecting { color: #F59E0B; }
        .status-disconnected { color: #EF4444; }

        .status-connected .status-dot { background: #10B981; }
        .status-connecting .status-dot { background: #F59E0B; }
        .status-disconnected .status-dot { background: #EF4444; }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        /* Loading States */
        .loading-spinner {
            width: 24px;
            height: 24px;
            border: 2px solid var(--border-color);
            border-top: 2px solid var(--neon-primary);
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .skeleton {
            background: linear-gradient(90deg, var(--bg-card) 25%, var(--bg-card-hover) 50%, var(--bg-card) 75%);
            background-size: 200% 100%;
            animation: shimmer 1.5s infinite;
        }

        @keyframes shimmer {
            0% { background-position: -200% 0; }
            100% { background-position: 200% 0; }
        }

        /* Output Area */
        .output-container {
            position: relative;
            min-height: 300px;
        }

        .output-area {
            background: rgba(0, 0, 0, 0.3);
            border: 1px solid var(--border-color);
            border-radius: var(--radius-md);
            padding: var(--space-lg);
            font-family: var(--font-mono);
            font-size: 0.9rem;
            line-height: 1.6;
            white-space: pre-wrap;
            word-wrap: break-word;
            max-height: 500px;
            overflow-y: auto;
            min-height: 300px;
        }

        .output-placeholder {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: 300px;
            color: var(--text-tertiary);
            text-align: center;
        }

        .output-placeholder i {
            font-size: 3rem;
            margin-bottom: var(--space-md);
            opacity: 0.5;
        }

        /* Utility Classes */
        .hidden { display: none !important; }
        .text-center { text-align: center; }
        .text-sm { font-size: 0.875rem; }
        .text-xs { font-size: 0.75rem; }
        .font-mono { font-family: var(--font-mono); }
        .font-semibold { font-weight: 600; }

        /* Statistics */
        .stats-container {
            display: flex;
            gap: var(--space-md);
            font-size: 0.85rem;
            color: var(--text-secondary);
        }

        .stat-item {
            display: flex;
            align-items: center;
            gap: var(--space-xs);
        }

        /* ============================================
           RESPONSIVE DESIGN
           ============================================ */

        @media (max-width: 1024px) {
            .content-grid {
                grid-template-columns: 1fr;
                gap: var(--space-lg);
            }

            .sidebar {
                order: 2;
            }

            .main-panel {
                order: 1;
            }
        }

        @media (max-width: 768px) {
            .main-content {
                padding: var(--space-md);
            }

            .header-content {
                padding: 0 var(--space-md);
            }

            .logo h1 {
                font-size: 1.2rem;
            }

            .card {
                padding: var(--space-lg);
            }

            .content-grid {
                gap: var(--space-md);
            }
        }

        /* ============================================
           ANIMATIONS & MICRO-INTERACTIONS
           ============================================ */

        .fade-in {
            animation: fadeIn 0.5s ease-out forwards;
        }

        .slide-up {
            animation: slideUp 0.4s ease-out forwards;
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        @keyframes slideUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* Focus states */
        .btn:focus-visible,
        .form-control:focus-visible {
            outline: 2px solid var(--neon-primary);
            outline-offset: 2px;
        }

        /* Processing animation for execute button */
        .btn.processing {
            position: relative;
            overflow: hidden;
        }

        .btn.processing::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            animation: shimmer 1.5s infinite;
        }

        /* Fade out animation for toast removal */
        @keyframes fadeOut {
            from { opacity: 1; transform: translateX(0); }
            to { opacity: 0; transform: translateX(100%); }
        }

        /* Reduced motion support */
        @media (prefers-reduced-motion: reduce) {
            * {
                animation-duration: 0.01ms !important;
                animation-iteration-count: 1 !important;
                transition-duration: 0.01ms !important;
            }
        }
    </style>
</head>
<body class="theme-dark">
    <div class="app-container">
        <!-- Header -->
        <header class="header">
            <div class="header-content">
                <div class="logo">
                    <div class="logo-icon">F</div>
                    <h1>Fabric Pattern Runner</h1>
                </div>
                <div class="header-controls">
                    <button id="themeToggle" class="btn btn-ghost btn-icon" title="Toggle Theme">
                        <i data-lucide="moon"></i>
                    </button>
                    <button id="settingsBtn" class="btn btn-ghost btn-icon" title="Settings">
                        <i data-lucide="settings"></i>
                    </button>
                    <button id="helpBtn" class="btn btn-ghost btn-icon" title="Help">
                        <i data-lucide="help-circle"></i>
                    </button>
                </div>
            </div>
        </header>

        <!-- Main Content -->
        <main class="main-content">
            <div class="content-grid">
                <!-- Sidebar -->
                <aside class="sidebar">
                    <!-- Pattern Selection -->
                    <div class="glass-card card fade-in">
                        <div class="card-header">
                            <h2 class="card-title">
                                <i data-lucide="layers"></i>
                                Select Pattern
                            </h2>
                            <button id="refreshPatterns" class="btn btn-ghost btn-icon" title="Refresh Patterns">
                                <i data-lucide="refresh-cw"></i>
                            </button>
                        </div>

                        <div class="form-group">
                            <label class="form-label" for="patternSelect">Pattern:</label>
                            <select id="patternSelect" class="form-control">
                                <option value="">Loading patterns...</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label class="form-label" for="modelSelect">Model (Optional):</label>
                            <select id="modelSelect" class="form-control">
                                <option value="">Auto (Default)</option>
                            </select>
                        </div>

                        <div id="patternInfo" class="glass-card" style="margin-top: var(--space-md); padding: var(--space-md);">
                            <p class="text-sm text-center" style="color: var(--text-tertiary);">
                                Select a pattern to see its description
                            </p>
                        </div>
                    </div>

                    <!-- Connection Status -->
                    <div class="glass-card card fade-in">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i data-lucide="activity"></i>
                                Status
                            </h3>
                        </div>

                        <div class="status-indicator" id="connectionStatus">
                            <div class="status-dot"></div>
                            <span>Checking connection...</span>
                        </div>

                        <div class="text-xs" style="margin-top: var(--space-sm); color: var(--text-tertiary);" id="lastExecution">
                            Ready to execute patterns
                        </div>
                    </div>
                </aside>

                <!-- Main Panel -->
                <div class="main-panel">
                    <!-- Input Section -->
                    <div class="glass-card card slide-up">
                        <div class="card-header">
                            <h2 class="card-title">
                                <i data-lucide="edit-3"></i>
                                Input Text
                            </h2>
                            <div class="stats-container">
                                <div class="stat-item">
                                    <i data-lucide="type"></i>
                                    <span id="charCount">0</span> chars
                                </div>
                                <div class="stat-item">
                                    <i data-lucide="file-text"></i>
                                    <span id="wordCount">0</span> words
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <textarea
                                id="inputText"
                                class="form-control mono textarea-large"
                                placeholder="Paste your text here or drag and drop a file..."
                                rows="8"
                            >This is a sample text to test the Fabric Pattern Runner. You can replace this with your own content and select a pattern to process it.</textarea>
                        </div>

                        <div style="display: flex; gap: var(--space-sm); margin-top: var(--space-md);">
                            <button id="clearInput" class="btn btn-secondary">
                                <i data-lucide="x"></i> Clear
                            </button>
                            <button id="pasteBtn" class="btn btn-secondary">
                                <i data-lucide="clipboard"></i> Paste
                            </button>
                            <input type="file" id="fileInput" accept=".txt,.md,.json,.csv" style="display: none;">
                            <button id="fileBtn" class="btn btn-secondary">
                                <i data-lucide="upload"></i> Upload File
                            </button>
                        </div>
                    </div>

                    <!-- Execute Section -->
                    <div class="glass-card card slide-up">
                        <div style="display: flex; align-items: center; justify-content: space-between; margin-bottom: var(--space-lg);">
                            <button id="executeBtn" class="btn btn-primary btn-large" disabled>
                                <i data-lucide="play"></i>
                                Execute Pattern
                            </button>

                            <button id="advancedToggle" class="btn btn-ghost">
                                <i data-lucide="chevron-down"></i>
                                Advanced Options
                            </button>
                        </div>

                        <!-- Advanced Options (Hidden by default) -->
                        <div id="advancedPanel" class="hidden glass-card" style="padding: var(--space-md); margin-bottom: var(--space-lg);">
                            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: var(--space-md);">
                                <div class="form-group">
                                    <label class="form-label" for="temperature">Temperature:</label>
                                    <input type="range" id="temperature" min="0" max="2" step="0.1" value="0.7" class="form-control">
                                    <div class="text-xs text-center" style="margin-top: var(--space-xs);">
                                        <span id="temperatureValue">0.7</span>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="form-label">
                                        <input type="checkbox" id="stream" style="margin-right: var(--space-xs);">
                                        Stream Response
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Output Section -->
                    <div class="glass-card card slide-up">
                        <div class="card-header">
                            <h2 class="card-title">
                                <i data-lucide="terminal"></i>
                                Output
                            </h2>
                            <div style="display: flex; gap: var(--space-sm);">
                                <button id="copyOutput" class="btn btn-ghost btn-icon" title="Copy to Clipboard" disabled>
                                    <i data-lucide="copy"></i>
                                </button>
                                <button id="downloadOutput" class="btn btn-ghost btn-icon" title="Download as Text" disabled>
                                    <i data-lucide="download"></i>
                                </button>
                                <button id="clearOutput" class="btn btn-ghost btn-icon" title="Clear Output" disabled>
                                    <i data-lucide="trash-2"></i>
                                </button>
                            </div>
                        </div>

                        <div class="output-container">
                            <div id="outputArea" class="output-area">
                                <div class="output-placeholder">
                                    <i data-lucide="cpu"></i>
                                    <p>Output will appear here after executing a pattern</p>
                                </div>
                            </div>

                            <div id="loadingIndicator" class="hidden" style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%);">
                                <div style="display: flex; flex-direction: column; align-items: center; gap: var(--space-md);">
                                    <div class="loading-spinner"></div>
                                    <p class="text-sm">Processing your request...</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- Settings Modal -->
    <div id="settingsModal" class="hidden" style="position: fixed; inset: 0; background: rgba(0, 0, 0, 0.5); backdrop-filter: blur(10px); display: flex; align-items: center; justify-content: center; z-index: 1000;">
        <div class="glass-card" style="width: 90%; max-width: 500px; padding: var(--space-xl);">
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: var(--space-lg);">
                <h3 style="font-size: 1.25rem; font-weight: 600;">Settings</h3>
                <button id="closeSettings" class="btn btn-ghost btn-icon">
                    <i data-lucide="x"></i>
                </button>
            </div>

            <div style="display: flex; flex-direction: column; gap: var(--space-lg);">
                <!-- API Configuration -->
                <div>
                    <h4 style="margin-bottom: var(--space-md); color: var(--text-primary); font-size: 1rem;">API Configuration</h4>
                    <div style="display: flex; flex-direction: column; gap: var(--space-md);">
                        <div class="form-group">
                            <label class="form-label" for="apiUrl">Fabric API URL:</label>
                            <input type="url" id="apiUrl" class="form-control" value="http://localhost:8080">
                        </div>
                        <div class="form-group">
                            <label class="form-label" for="apiKey">API Key (if required):</label>
                            <input type="password" id="apiKey" class="form-control" placeholder="Enter API key">
                        </div>
                    </div>
                </div>

                <!-- Provider API Keys -->
                <div>
                    <h4 style="margin-bottom: var(--space-md); color: var(--text-primary); font-size: 1rem;">LLM Provider API Keys</h4>
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: var(--space-md);">
                        <div class="form-group">
                            <label class="form-label" for="openaiKey">OpenAI API Key:</label>
                            <input type="password" id="openaiKey" class="form-control" placeholder="sk-...">
                        </div>
                        <div class="form-group">
                            <label class="form-label" for="anthropicKey">Anthropic API Key:</label>
                            <input type="password" id="anthropicKey" class="form-control" placeholder="sk-ant-...">
                        </div>
                        <div class="form-group">
                            <label class="form-label" for="googleKey">Google API Key:</label>
                            <input type="password" id="googleKey" class="form-control" placeholder="AIza...">
                        </div>
                        <div class="form-group">
                            <label class="form-label" for="groqKey">Groq API Key:</label>
                            <input type="password" id="groqKey" class="form-control" placeholder="gsk_...">
                        </div>
                        <div class="form-group">
                            <label class="form-label" for="ollamaUrl">Ollama URL:</label>
                            <input type="url" id="ollamaUrl" class="form-control" placeholder="http://localhost:11434">
                        </div>
                        <div class="form-group">
                            <label class="form-label" for="xaiKey">xAI API Key:</label>
                            <input type="password" id="xaiKey" class="form-control" placeholder="xai-...">
                        </div>
                    </div>
                </div>

                <!-- General Settings -->
                <div>
                    <h4 style="margin-bottom: var(--space-md); color: var(--text-primary); font-size: 1rem;">General Settings</h4>
                    <div style="display: flex; flex-direction: column; gap: var(--space-md);">
                        <div class="form-group">
                            <label class="form-label" for="timeout">Request Timeout (seconds):</label>
                            <input type="number" id="timeout" class="form-control" value="30" min="5" max="300">
                        </div>
                        <div class="form-group">
                            <label class="form-label">
                                <input type="checkbox" id="autoSave" checked style="margin-right: var(--space-xs);">
                                Auto-save settings
                            </label>
                        </div>
                        <div class="form-group">
                            <label class="form-label">
                                <input type="checkbox" id="enableStreaming" checked style="margin-right: var(--space-xs);">
                                Enable streaming by default
                            </label>
                        </div>
                    </div>
                </div>
            </div>

            <div style="display: flex; justify-content: flex-end; gap: var(--space-sm); margin-top: var(--space-xl);">
                <button id="cancelSettings" class="btn btn-secondary">Cancel</button>
                <button id="saveSettings" class="btn btn-primary">Save</button>
            </div>
        </div>
    </div>

    <!-- Toast Notifications -->
    <div id="toastContainer" style="position: fixed; top: 20px; right: 20px; z-index: 1100; display: flex; flex-direction: column; gap: var(--space-sm); pointer-events: none;">
        <!-- Toasts will be dynamically inserted here -->
    </div>

    <!-- Enhanced JavaScript -->
    <script src="fabric-api-enhanced.js"></script>
    <script>
        // Initialize Lucide icons
        lucide.createIcons();

        // Initialize theme toggle (enhanced)
        document.getElementById('themeToggle').addEventListener('click', function() {
            const body = document.body;
            const isDark = body.classList.contains('theme-dark');

            body.className = isDark ? 'theme-light' : 'theme-dark';

            const icon = this.querySelector('i');
            icon.setAttribute('data-lucide', isDark ? 'sun' : 'moon');
            lucide.createIcons();

            localStorage.setItem('theme', isDark ? 'light' : 'dark');

            if (window.fabricApp && window.fabricApp.ui) {
                window.fabricApp.ui.showToast(`Switched to ${isDark ? 'light' : 'dark'} theme`, 'success');
            }
        });

        // Load saved theme
        const savedTheme = localStorage.getItem('theme') || 'dark';
        document.body.className = `theme-${savedTheme}`;

        // Update theme icon
        const themeIcon = document.querySelector('#themeToggle i');
        themeIcon.setAttribute('data-lucide', savedTheme === 'dark' ? 'moon' : 'sun');
        lucide.createIcons();

        // Settings modal handlers (enhanced)
        document.getElementById('settingsBtn').addEventListener('click', () => {
            document.getElementById('settingsModal').classList.remove('hidden');
        });

        document.getElementById('closeSettings').addEventListener('click', () => {
            document.getElementById('settingsModal').classList.add('hidden');
        });

        document.getElementById('cancelSettings').addEventListener('click', () => {
            document.getElementById('settingsModal').classList.add('hidden');
        });

        // Advanced options toggle
        document.getElementById('advancedToggle').addEventListener('click', function() {
            const panel = document.getElementById('advancedPanel');
            const icon = this.querySelector('i');
            const isHidden = panel.classList.contains('hidden');

            if (isHidden) {
                panel.classList.remove('hidden');
                icon.setAttribute('data-lucide', 'chevron-up');
            } else {
                panel.classList.add('hidden');
                icon.setAttribute('data-lucide', 'chevron-down');
            }
            lucide.createIcons();
        });

        // Temperature slider
        document.getElementById('temperature').addEventListener('input', function() {
            document.getElementById('temperatureValue').textContent = this.value;
        });

        console.log('Modern Fabric GUI initialized successfully!');

        // Add test functionality for debugging
        window.runGUITests = function() {
            console.log('=== Running GUI Tests ===');

            // Test 1: Pattern Loading
            if (window.fabricApp && window.fabricApp.api) {
                window.fabricApp.api.getPatterns().then(patterns => {
                    console.log('✅ Pattern Loading:', patterns.length, 'patterns loaded');

                    // Test 2: Pattern Dropdown
                    const select = document.getElementById('patternSelect');
                    if (select && select.options.length > 1) {
                        console.log('✅ Pattern Dropdown:', select.options.length, 'options available');

                        // Test 3: Pattern Selection
                        select.value = select.options[1].value;
                        select.dispatchEvent(new Event('change'));
                        console.log('✅ Pattern Selection: Selected', select.value);

                        // Test 4: Execute Button State
                        setTimeout(() => {
                            const executeBtn = document.getElementById('executeBtn');
                            if (executeBtn) {
                                console.log('✅ Execute Button:', executeBtn.disabled ? 'disabled' : 'enabled');
                            }
                        }, 100);
                    } else {
                        console.log('❌ Pattern Dropdown: Not populated');
                    }
                }).catch(error => {
                    console.log('❌ Pattern Loading Error:', error.message);
                });
            } else {
                console.log('❌ Fabric App: Not initialized');
            }

            // Test 5: UI Elements
            const elements = {
                'patternSelect': 'Pattern Select',
                'inputText': 'Input Text Area',
                'executeBtn': 'Execute Button',
                'connectionStatus': 'Connection Status',
                'charCount': 'Character Count',
                'wordCount': 'Word Count',
                'outputArea': 'Output Area',
                'themeToggle': 'Theme Toggle',
                'settingsBtn': 'Settings Button'
            };

            Object.entries(elements).forEach(([id, name]) => {
                const element = document.getElementById(id);
                console.log(element ? '✅' : '❌', name + ':', element ? 'found' : 'not found');
            });

            console.log('=== GUI Tests Complete ===');
        };

        // Add status check function
        window.checkGUIStatus = function() {
            console.log('=== GUI Status Check ===');
            console.log('Fabric App:', window.fabricApp ? 'Initialized' : 'Not initialized');
            console.log('API Connected:', window.fabricApp?.api?.isConnected ? 'Yes' : 'No');
            console.log('Current Pattern:', window.fabricApp?.currentPattern || 'None selected');
            console.log('Patterns Loaded:', window.fabricApp?.api?.patterns?.length || 0);

            const inputText = document.getElementById('inputText');
            console.log('Input Text Length:', inputText?.value?.length || 0);

            const executeBtn = document.getElementById('executeBtn');
            console.log('Execute Button:', executeBtn?.disabled ? 'Disabled' : 'Enabled');

            console.log('=== Status Check Complete ===');
        };

        // Add quick test button (for debugging)
        if (window.location.search.includes('debug')) {
            const testBtn = document.createElement('button');
            testBtn.textContent = 'Run Tests';
            testBtn.style.position = 'fixed';
            testBtn.style.top = '10px';
            testBtn.style.right = '10px';
            testBtn.style.zIndex = '9999';
            testBtn.style.background = '#007bff';
            testBtn.style.color = 'white';
            testBtn.style.border = 'none';
            testBtn.style.padding = '8px 16px';
            testBtn.style.borderRadius = '4px';
            testBtn.style.cursor = 'pointer';
            testBtn.onclick = window.runGUITests;
            document.body.appendChild(testBtn);

            const statusBtn = document.createElement('button');
            statusBtn.textContent = 'Check Status';
            statusBtn.style.position = 'fixed';
            statusBtn.style.top = '50px';
            statusBtn.style.right = '10px';
            statusBtn.style.zIndex = '9999';
            statusBtn.style.background = '#28a745';
            statusBtn.style.color = 'white';
            statusBtn.style.border = 'none';
            statusBtn.style.padding = '8px 16px';
            statusBtn.style.borderRadius = '4px';
            statusBtn.style.cursor = 'pointer';
            statusBtn.onclick = window.checkGUIStatus;
            document.body.appendChild(statusBtn);
        }
    </script>
</body>
</html>
