/**
 * ENHANCED FABRIC API INTEGRATION
 * Modern JavaScript API layer for Fabric Pattern Runner
 */

class EnhancedFabricAPI {
    constructor() {
        this.baseUrl = 'http://localhost:8080';
        this.patterns = [];
        this.models = [];
        this.isConnected = false;
        this.timeout = 30000; // 30 seconds
        this.apiKey = null;

        // Load settings
        this.loadSettings();
    }

    // ============================================
    // CONFIGURATION & SETTINGS
    // ============================================

    loadSettings() {
        try {
            const settings = JSON.parse(localStorage.getItem('fabricApiSettings') || '{}');
            this.baseUrl = settings.baseUrl || 'http://localhost:8080';
            this.apiKey = settings.apiKey || null;
            this.timeout = settings.timeout || 30000;
        } catch (error) {
            console.warn('Error loading settings:', error);
        }
    }

    saveSettings(settings) {
        try {
            const currentSettings = JSON.parse(localStorage.getItem('fabricApiSettings') || '{}');
            const newSettings = { ...currentSettings, ...settings };
            localStorage.setItem('fabricApiSettings', JSON.stringify(newSettings));

            // Update instance properties
            if (newSettings.baseUrl) this.baseUrl = newSettings.baseUrl;
            if (newSettings.apiKey !== undefined) this.apiKey = newSettings.apiKey;
            if (newSettings.timeout) this.timeout = newSettings.timeout;

            return true;
        } catch (error) {
            console.error('Error saving settings:', error);
            return false;
        }
    }

    // ============================================
    // HTTP UTILITIES
    // ============================================

    async makeRequest(endpoint, options = {}) {
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), this.timeout);

        try {
            const url = endpoint.startsWith('http') ? endpoint : `${this.baseUrl}${endpoint}`;

            const defaultHeaders = {
                'Accept': 'application/json, text/plain, */*'
            };

            // Add API key if available
            if (this.apiKey) {
                defaultHeaders['Authorization'] = `Bearer ${this.apiKey}`;
            }

            const response = await fetch(url, {
                ...options,
                headers: {
                    ...defaultHeaders,
                    ...options.headers
                },
                signal: controller.signal
            });

            clearTimeout(timeoutId);

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            return response;
        } catch (error) {
            clearTimeout(timeoutId);

            if (error.name === 'AbortError') {
                throw new Error(`Request timeout after ${this.timeout}ms`);
            }

            throw error;
        }
    }

    async get(endpoint) {
        const response = await this.makeRequest(endpoint);
        const contentType = response.headers.get('content-type');

        if (contentType && contentType.includes('application/json')) {
            return await response.json();
        } else {
            return await response.text();
        }
    }

    async post(endpoint, data, headers = {}) {
        const response = await this.makeRequest(endpoint, {
            method: 'POST',
            body: typeof data === 'string' ? data : JSON.stringify(data),
            headers: {
                'Content-Type': typeof data === 'string' ? 'text/plain' : 'application/json',
                ...headers
            }
        });

        const contentType = response.headers.get('content-type');
        if (contentType && contentType.includes('application/json')) {
            return await response.json();
        } else {
            return await response.text();
        }
    }

    // ============================================
    // CONNECTION MANAGEMENT
    // ============================================

    async testConnection() {
        try {
            console.log('Testing Fabric API connection...');

            // Try to fetch patterns endpoint to test connectivity
            await this.get('/patterns/names');
            this.isConnected = true;
            return true;

        } catch (error) {
            console.warn('API connection test failed:', error);
            this.isConnected = false;
            return false;
        }
    }

    // ============================================
    // PATTERN MANAGEMENT
    // ============================================

    async loadPatternsDirect() {
        // Default patterns for development/demo mode
        const defaultPatterns = [
            'summarize', 'analyze', 'extract_wisdom', 'improve_writing', 'explain_code',
            'create_pattern', 'find_logical_fallacies', 'write_essay', 'create_quiz',
            'extract_insights', 'write_micro_essay', 'create_keynote'
        ];

        return defaultPatterns.map(name => ({
            name: name,
            description: `Execute ${name.replace(/_/g, ' ')} pattern`
        }));
    }

    async getPatterns(forceRefresh = false) {
        console.log('getPatterns called, forceRefresh:', forceRefresh, 'current patterns count:', this.patterns.length);

        if (this.patterns.length > 0 && !forceRefresh) {
            console.log('Returning cached patterns:', this.patterns.length);
            return this.patterns;
        }

        try {
            console.log('Attempting to fetch patterns from API...');
            // Try to get patterns from API first
            const patternNames = await this.get('/patterns/names');
            console.log('API response for patterns:', patternNames);

            if (Array.isArray(patternNames) && patternNames.length > 0) {
                this.patterns = patternNames.map(name => ({
                    name: name,
                    description: `Execute ${name.replace(/_/g, ' ')} pattern`
                }));
                console.log('Successfully loaded patterns from API:', this.patterns.length);
            } else {
                console.log('API returned empty or invalid pattern list, using fallback');
                this.patterns = await this.loadPatternsDirect();
            }

            // Cache patterns
            localStorage.setItem('fabricPatterns', JSON.stringify(this.patterns));

            return this.patterns;
        } catch (error) {
            console.error('Error loading patterns from API:', error);

            // Try to load from cache
            try {
                const cached = JSON.parse(localStorage.getItem('fabricPatterns') || '[]');
                if (cached.length > 0) {
                    console.log('Using cached patterns:', cached.length);
                    this.patterns = cached;
                    return this.patterns;
                }
            } catch (cacheError) {
                console.warn('Error loading cached patterns:', cacheError);
            }

            // Load default patterns as fallback
            console.log('Loading default patterns as final fallback');
            this.patterns = await this.loadPatternsDirect();
            console.log('Loaded default patterns:', this.patterns.length);
            return this.patterns;
        }
    }

    getPatternInfo(patternName) {
        return this.patterns.find(p => p.name === patternName) || null;
    }

    // ============================================
    // MODEL MANAGEMENT
    // ============================================

    async getModels(forceRefresh = false) {
        if (this.models.length > 0 && !forceRefresh) {
            return this.models;
        }

        try {
            // Latest AI models as of 2025
            this.models = [
                // OpenAI Models
                { name: 'gpt-4o', provider: 'OpenAI', description: 'Most capable GPT-4 Omni model' },
                { name: 'gpt-4o-mini', provider: 'OpenAI', description: 'Faster, cheaper GPT-4 Omni' },
                { name: 'gpt-4-turbo', provider: 'OpenAI', description: 'GPT-4 Turbo with vision' },
                { name: 'gpt-4', provider: 'OpenAI', description: 'Original GPT-4 model' },
                { name: 'gpt-3.5-turbo', provider: 'OpenAI', description: 'Fast and affordable model' },
                { name: 'o1-preview', provider: 'OpenAI', description: 'Advanced reasoning model' },
                { name: 'o1-mini', provider: 'OpenAI', description: 'Faster reasoning model' },

                // Anthropic Claude Models
                { name: 'claude-3-5-sonnet-20241022', provider: 'Anthropic', description: 'Latest Claude 3.5 Sonnet' },
                { name: 'claude-3-5-sonnet-20240620', provider: 'Anthropic', description: 'Claude 3.5 Sonnet' },
                { name: 'claude-3-5-haiku-20241022', provider: 'Anthropic', description: 'Latest Claude 3.5 Haiku' },
                { name: 'claude-3-opus-20240229', provider: 'Anthropic', description: 'Most capable Claude 3 model' },
                { name: 'claude-3-sonnet-20240229', provider: 'Anthropic', description: 'Balanced Claude 3 model' },
                { name: 'claude-3-haiku-20240307', provider: 'Anthropic', description: 'Fastest Claude 3 model' },

                // Google Gemini Models
                { name: 'gemini-2.0-flash-exp', provider: 'Google', description: 'Experimental Gemini 2.0 Flash' },
                { name: 'gemini-1.5-pro-002', provider: 'Google', description: 'Latest Gemini 1.5 Pro' },
                { name: 'gemini-1.5-pro', provider: 'Google', description: 'Gemini 1.5 Pro' },
                { name: 'gemini-1.5-flash', provider: 'Google', description: 'Fast Gemini 1.5 model' },
                { name: 'gemini-1.5-flash-8b', provider: 'Google', description: 'Lightweight Gemini Flash' },
                { name: 'gemini-pro', provider: 'Google', description: 'Gemini Pro model' },

                // Meta Llama Models
                { name: 'llama-3.3-70b-versatile', provider: 'Meta', description: 'Latest Llama 3.3 70B' },
                { name: 'llama-3.2-90b-text-preview', provider: 'Meta', description: 'Llama 3.2 90B preview' },
                { name: 'llama-3.2-11b-text-preview', provider: 'Meta', description: 'Llama 3.2 11B preview' },
                { name: 'llama-3.1-405b-reasoning', provider: 'Meta', description: 'Largest Llama 3.1 model' },
                { name: 'llama-3.1-70b-versatile', provider: 'Meta', description: 'Versatile Llama 3.1 70B' },
                { name: 'llama-3.1-8b-instant', provider: 'Meta', description: 'Fast Llama 3.1 8B' },

                // Mistral AI Models
                { name: 'mistral-large-2407', provider: 'Mistral AI', description: 'Latest Mistral Large' },
                { name: 'mistral-medium-2312', provider: 'Mistral AI', description: 'Mistral Medium model' },
                { name: 'mistral-small-2402', provider: 'Mistral AI', description: 'Mistral Small model' },
                { name: 'mixtral-8x7b-32768', provider: 'Mistral AI', description: 'Mixtral 8x7B model' },
                { name: 'mixtral-8x22b-32768', provider: 'Mistral AI', description: 'Mixtral 8x22B model' },

                // xAI Models
                { name: 'grok-beta', provider: 'xAI', description: 'Grok AI model' },
                { name: 'grok-vision-beta', provider: 'xAI', description: 'Grok with vision capabilities' },

                // Cohere Models
                { name: 'command-r-plus', provider: 'Cohere', description: 'Command R+ model' },
                { name: 'command-r', provider: 'Cohere', description: 'Command R model' },
                { name: 'command-light', provider: 'Cohere', description: 'Lightweight Command model' },

                // Perplexity Models
                { name: 'llama-3.1-sonar-large-128k-online', provider: 'Perplexity', description: 'Sonar Large with web search' },
                { name: 'llama-3.1-sonar-small-128k-online', provider: 'Perplexity', description: 'Sonar Small with web search' },
                { name: 'llama-3.1-sonar-huge-128k-online', provider: 'Perplexity', description: 'Sonar Huge with web search' },

                // Together AI Models
                { name: 'meta-llama/Llama-3.2-90B-Vision-Instruct-Turbo', provider: 'Together AI', description: 'Llama 3.2 90B Vision' },
                { name: 'meta-llama/Llama-Vision-Free', provider: 'Together AI', description: 'Free Llama Vision model' },

                // DeepSeek Models
                { name: 'deepseek-chat', provider: 'DeepSeek', description: 'DeepSeek Chat model' },
                { name: 'deepseek-coder', provider: 'DeepSeek', description: 'DeepSeek Coder model' },

                // Local/Ollama Models (commonly used)
                { name: 'llama3.2:latest', provider: 'Ollama', description: 'Local Llama 3.2' },
                { name: 'llama3.1:latest', provider: 'Ollama', description: 'Local Llama 3.1' },
                { name: 'mistral:latest', provider: 'Ollama', description: 'Local Mistral' },
                { name: 'codellama:latest', provider: 'Ollama', description: 'Local Code Llama' },
                { name: 'qwen2.5:latest', provider: 'Ollama', description: 'Local Qwen 2.5' },
            ];

            // Cache models
            localStorage.setItem('fabricModels', JSON.stringify(this.models));

            return this.models;
        } catch (error) {
            console.error('Error loading models:', error);

            // Try to load from cache
            try {
                const cached = JSON.parse(localStorage.getItem('fabricModels') || '[]');
                if (cached.length > 0) {
                    this.models = cached;
                    return this.models;
                }
            } catch (cacheError) {
                console.warn('Error loading cached models:', cacheError);
            }

            return [];
        }
    }

    // ============================================
    // PATTERN EXECUTION
    // ============================================

    async executePattern(patternName, inputText, options = {}) {
        if (!patternName || !inputText?.trim()) {
            throw new Error('Pattern name and input text are required');
        }

        try {
            console.log(`Executing pattern: ${patternName}`);
            console.log(`Input length: ${inputText.length} characters`);

            // Try to execute pattern via API
            try {
                const response = await this.post(`/patterns/${patternName}/apply`, inputText, {
                    'Content-Type': 'text/plain'
                });
                return response;
            } catch (apiError) {
                console.warn('API execution failed, using simulation:', apiError);
                // Fall back to simulation
                return await this.simulatePatternExecution(patternName, inputText, options);
            }

        } catch (error) {
            console.error(`Error executing pattern '${patternName}':`, error);
            throw new Error(`Failed to execute pattern: ${error.message}`);
        }
    }

    async executePatternStream(patternName, inputText, onChunk, options = {}) {
        if (!patternName || !inputText?.trim()) {
            throw new Error('Pattern name and input text are required');
        }

        try {
            console.log(`Streaming pattern: ${patternName}`);

            // Try streaming via API first
            try {
                const response = await this.post(`/patterns/${patternName}?stream=true`, inputText, {
                    'Content-Type': 'text/plain'
                });

                const reader = response.body.getReader();
                const decoder = new TextDecoder();
                let result = '';

                while (true) {
                    const { done, value } = await reader.read();

                    if (done) break;

                    const chunk = decoder.decode(value, { stream: true });
                    result += chunk;

                    if (onChunk) {
                        onChunk(chunk);
                    }
                }

                return result;
            } catch (apiError) {
                console.warn('API streaming failed, using simulation:', apiError);
                // Fall back to simulated streaming
                const result = await this.simulatePatternExecution(patternName, inputText, options);

                // Simulate streaming chunks
                const chunks = result.split(' ');
                for (let i = 0; i < chunks.length; i++) {
                    const chunk = chunks[i] + (i < chunks.length - 1 ? ' ' : '');
                    if (onChunk) {
                        onChunk(chunk);
                    }
                    await new Promise(resolve => setTimeout(resolve, 50)); // Simulate delay
                }

                return result;
            }

            return result;

        } catch (error) {
            console.error(`Error streaming pattern '${patternName}':`, error);
            throw new Error(`Failed to stream pattern: ${error.message}`);
        }
    }

    // ============================================
    // PATTERN SIMULATION (DEVELOPMENT)
    // ============================================

    async simulatePatternExecution(patternName, inputText, options = {}) {
        // Simulate processing delay
        await new Promise(resolve => setTimeout(resolve, 1000 + Math.random() * 2000));

        const responses = {
            'summarize': `# Summary\n\nThis text discusses various topics and presents key information in a structured format. The main points include:\n\n- Key concept 1: Extracted from the input text\n- Key concept 2: Important information identified\n- Key concept 3: Summary of main themes\n\n**Word Count:** ${inputText.split(/\s+/).length} words\n**Character Count:** ${inputText.length} characters\n\nThe content has been processed and condensed into this concise summary.`,

            'extract_wisdom': `# Extracted Wisdom\n\n## Key Insights\n- Insight 1: Important lesson from the text\n- Insight 2: Valuable principle identified\n- Insight 3: Actionable knowledge extracted\n\n## Practical Applications\n- Application 1: How to apply this knowledge\n- Application 2: Real-world implementation\n- Application 3: Next steps for action\n\n## Memorable Quotes\n- "Important quote extracted from text"\n- "Another significant statement"\n\n**Processing Temperature:** ${options.temperature || 0.7}\n**Model:** ${options.model || 'Auto'}`,

            'explain_code': `# Code Explanation\n\n## Overview\nThe provided code performs the following functions:\n\n## Key Components\n1. **Main Function**: Core logic implementation\n2. **Helper Methods**: Supporting functionality\n3. **Data Structures**: Information organization\n\n## Flow Analysis\n- Step 1: Initial processing\n- Step 2: Data transformation\n- Step 3: Output generation\n\n## Potential Improvements\n- Optimization opportunity 1\n- Enhancement suggestion 2\n- Best practice recommendation\n\n**Analysis completed using Fabric Pattern Runner**`,

            'improve_writing': `# Writing Improvement Suggestions\n\n## Strengths\n- Clear communication style\n- Good structure and organization\n- Appropriate tone for audience\n\n## Areas for Enhancement\n1. **Clarity**: Some sentences could be simplified\n2. **Flow**: Transitions between paragraphs could be smoother\n3. **Engagement**: Consider adding more vivid examples\n\n## Specific Recommendations\n- Use more active voice where appropriate\n- Vary sentence length for better rhythm\n- Add concrete examples to support abstract concepts\n\n## Revised Version\n[The text would include suggested improvements here]\n\n**Analysis powered by Fabric AI**`,

            default: `# Pattern Execution Result\n\n**Pattern:** ${patternName}\n**Input Length:** ${inputText.length} characters\n**Processing Options:** ${JSON.stringify(options, null, 2)}\n\n## Analysis\nThe ${patternName.replace(/_/g, ' ')} pattern has been applied to your input text. This is a simulated response showing how the pattern would process your content.\n\n## Key Findings\n- Finding 1: Extracted from input analysis\n- Finding 2: Pattern-specific insight\n- Finding 3: Processing result\n\n## Output\nYour text has been successfully processed using the ${patternName} pattern. In a real implementation, this would show the actual pattern execution results from the Fabric AI system.\n\n**Generated by Fabric Pattern Runner - Demo Mode**\n\n*Note: This is a simulated response. Connect to a running Fabric server for actual pattern execution.*`
        };

        return responses[patternName] || responses.default;
    }

    // ============================================
    // VALIDATION
    // ============================================

    validateInput(text) {
        const result = {
            isValid: true,
            errors: [],
            warnings: []
        };

        if (!text || text.trim() === '') {
            result.isValid = false;
            result.errors.push('Input text is required');
        }

        if (text && text.length > 100000) { // 100KB limit
            result.warnings.push('Input text is very long and may take time to process');
        }

        if (text && text.length < 10) {
            result.warnings.push('Input text is very short, results may be limited');
        }

        return result;
    }

    isValidPattern(patternName) {
        return this.patterns.some(p => p.name === patternName);
    }

    getConnectionStatus() {
        return this.isConnected;
    }
}

// ============================================
// ENHANCED UI MANAGER
// ============================================

class EnhancedUIManager {
    constructor() {
        this.toasts = [];
        this.modals = new Set();
    }

    showToast(message, type = 'info', duration = 3000) {
        const container = document.getElementById('toastContainer');
        if (!container) return;

        const toast = document.createElement('div');
        const toastId = Date.now() + Math.random();

        const icons = {
            success: 'check-circle',
            error: 'alert-circle',
            warning: 'alert-triangle',
            info: 'info'
        };

        const colors = {
            success: '#10B981',
            error: '#EF4444',
            warning: '#F59E0B',
            info: '#3B82F6'
        };

        toast.innerHTML = `
            <div class="glass-card" style="padding: var(--space-md); display: flex; align-items: center; gap: var(--space-sm); border-left: 3px solid ${colors[type]}; animation: slideUp 0.3s ease-out;">
                <i data-lucide="${icons[type]}" style="color: ${colors[type]}; min-width: 20px;"></i>
                <span style="color: var(--text-primary); flex: 1;">${message}</span>
                <button onclick="this.closest('[data-toast]').remove()" style="background: none; border: none; color: var(--text-tertiary); cursor: pointer; padding: 4px;">
                    <i data-lucide="x" style="width: 16px; height: 16px;"></i>
                </button>
            </div>
        `;

        toast.setAttribute('data-toast', toastId);
        container.appendChild(toast);

        // Initialize Lucide icons for this toast
        if (window.lucide) {
            window.lucide.createIcons();
        }

        // Auto-remove after duration
        setTimeout(() => {
            if (toast.parentElement) {
                toast.style.animation = 'fadeOut 0.3s ease-out forwards';
                setTimeout(() => {
                    if (toast.parentElement) {
                        toast.remove();
                    }
                }, 300);
            }
        }, duration);

        return toastId;
    }

    updateConnectionStatus(isConnected) {
        const statusElement = document.getElementById('connectionStatus');
        if (!statusElement) return;

        const dot = statusElement.querySelector('.status-dot');
        const text = statusElement.querySelector('span');

        if (isConnected) {
            statusElement.className = 'status-indicator status-connected';
            if (dot) dot.style.background = '#10B981';
            if (text) text.textContent = 'Connected to Fabric API';
        } else {
            statusElement.className = 'status-indicator status-disconnected';
            if (dot) dot.style.background = '#EF4444';
            if (text) text.textContent = 'Disconnected (Demo Mode)';
        }
    }

    updateExecuteButton(isExecuting, canExecute) {
        const btn = document.getElementById('executeBtn');
        if (!btn) return;

        btn.disabled = !canExecute || isExecuting;

        const icon = btn.querySelector('i');
        const text = btn.querySelector('span') || btn;

        if (isExecuting) {
            if (icon) icon.setAttribute('data-lucide', 'loader');
            text.textContent = isExecuting ? 'Processing...' : 'Execute Pattern';
            btn.classList.add('processing');
        } else {
            if (icon) icon.setAttribute('data-lucide', 'play');
            text.textContent = 'Execute Pattern';
            btn.classList.remove('processing');
        }

        if (window.lucide) {
            window.lucide.createIcons();
        }
    }

    enableOutputActions() {
        ['copyOutput', 'downloadOutput', 'clearOutput'].forEach(id => {
            const btn = document.getElementById(id);
            if (btn) btn.disabled = false;
        });
    }

    showLoading(containerId) {
        const container = document.getElementById(containerId);
        if (container) {
            container.style.opacity = '0.6';
            container.style.pointerEvents = 'none';
        }
    }

    hideLoading(containerId) {
        const container = document.getElementById(containerId);
        if (container) {
            container.style.opacity = '1';
            container.style.pointerEvents = 'auto';
        }
    }
}

// ============================================
// ENHANCED APPLICATION
// ============================================

class EnhancedFabricApp {
    constructor() {
        this.api = new EnhancedFabricAPI();
        this.ui = new EnhancedUIManager();
        this.currentPattern = null;
        this.currentModel = null;
        this.isExecuting = false;

        this.init();
    }

    async init() {
        console.log('Initializing Enhanced Fabric Pattern Runner...');

        try {
            // Setup event listeners
            console.log('Setting up event listeners...');
            this.setupEventListeners();

            // Test API connection
            console.log('Testing API connection...');
            const isConnected = await this.api.testConnection();
            console.log('API connection test result:', isConnected);
            this.ui.updateConnectionStatus(isConnected);

            if (isConnected) {
                this.ui.showToast('Connected to Fabric API server', 'success');
            } else {
                this.ui.showToast('Running in demo mode - connect Fabric server for full functionality', 'warning', 5000);
            }

            // Load initial data
            console.log('Loading patterns...');
            await this.loadPatterns();
            console.log('Loading models...');
            await this.loadModels();

            // Update initial UI state
            this.updateTextStats();
            this.updateExecuteButton();

            this.ui.showToast('Fabric Pattern Runner ready!', 'success');
            console.log('Initialization complete!');

        } catch (error) {
            console.error('Error initializing app:', error);
            this.ui.showToast('Error initializing application: ' + error.message, 'error');
        }
    }

    setupEventListeners() {
        // Pattern selection
        const patternSelect = document.getElementById('patternSelect');
        if (patternSelect) {
            patternSelect.addEventListener('change', (e) => {
                console.log('Pattern selected:', e.target.value);
                this.currentPattern = e.target.value;
                this.updatePatternInfo();
                this.updateExecuteButton();
            });
        } else {
            console.error('Pattern select element not found during event listener setup!');
        }

        // Model selection
        const modelSelect = document.getElementById('modelSelect');
        if (modelSelect) {
            modelSelect.addEventListener('change', (e) => {
                this.currentModel = e.target.value || null;
            });
        }

        // Input text
        const inputText = document.getElementById('inputText');
        if (inputText) {
            inputText.addEventListener('input', () => {
                this.updateTextStats();
                this.updateExecuteButton();
            });
        }

        // Execute button
        const executeBtn = document.getElementById('executeBtn');
        if (executeBtn) {
            executeBtn.addEventListener('click', () => {
                this.executePattern();
            });
        }

        // Refresh patterns
        const refreshBtn = document.getElementById('refreshPatterns');
        if (refreshBtn) {
            refreshBtn.addEventListener('click', () => {
                this.loadPatterns(true);
            });
        }

        // Output actions
        document.getElementById('copyOutput')?.addEventListener('click', () => this.copyOutput());
        document.getElementById('downloadOutput')?.addEventListener('click', () => this.downloadOutput());
        document.getElementById('clearOutput')?.addEventListener('click', () => this.clearOutput());

        // Settings
        document.getElementById('saveSettings')?.addEventListener('click', () => this.saveSettings());

        // Load settings into form when modal opens
        document.getElementById('settingsBtn')?.addEventListener('click', () => this.loadSettingsIntoForm());
    }

    async loadPatterns(forceRefresh = false) {
        console.log('loadPatterns called, forceRefresh:', forceRefresh);
        try {
            this.ui.showLoading('patternSelect');

            const patterns = await this.api.getPatterns(forceRefresh);
            console.log('Patterns received in loadPatterns:', patterns);

            const select = document.getElementById('patternSelect');
            if (select) {
                select.innerHTML = '<option value="">Select a pattern...</option>';

                patterns.forEach(pattern => {
                    const option = document.createElement('option');
                    option.value = pattern.name;
                    option.textContent = pattern.name;
                    select.appendChild(option);
                });

                console.log('Pattern dropdown populated with', patterns.length, 'patterns');
            } else {
                console.error('Pattern select element not found!');
            }

            if (forceRefresh) {
                this.ui.showToast(`Loaded ${patterns.length} patterns`, 'success');
            } else {
                this.ui.showToast(`Loaded ${patterns.length} patterns`, 'success');
            }

        } catch (error) {
            console.error('Error loading patterns:', error);
            this.ui.showToast('Error loading patterns: ' + error.message, 'error');
        } finally {
            this.ui.hideLoading('patternSelect');
        }
    }

    async loadModels(forceRefresh = false) {
        try {
            const models = await this.api.getModels(forceRefresh);

            const select = document.getElementById('modelSelect');
            if (select && models.length > 0) {
                // Keep the "Auto" option
                const autoOption = select.querySelector('option[value=""]');
                select.innerHTML = '';
                if (autoOption) select.appendChild(autoOption);

                // Group by provider
                const grouped = {};
                models.forEach(model => {
                    const provider = model.provider || 'Other';
                    if (!grouped[provider]) grouped[provider] = [];
                    grouped[provider].push(model);
                });

                Object.entries(grouped).forEach(([provider, providerModels]) => {
                    const optgroup = document.createElement('optgroup');
                    optgroup.label = provider;

                    providerModels.forEach(model => {
                        const option = document.createElement('option');
                        option.value = model.name;
                        option.textContent = model.name;
                        optgroup.appendChild(option);
                    });

                    select.appendChild(optgroup);
                });
            }

        } catch (error) {
            console.error('Error loading models:', error);
        }
    }

    updatePatternInfo() {
        const infoElement = document.getElementById('patternInfo');
        if (!infoElement) return;

        if (this.currentPattern) {
            const pattern = this.api.getPatternInfo(this.currentPattern);
            infoElement.innerHTML = `
                <p class="text-sm" style="color: var(--text-primary); margin-bottom: var(--space-xs);">
                    <strong>${this.currentPattern}</strong>
                </p>
                <p class="text-xs" style="color: var(--text-tertiary);">
                    ${pattern ? pattern.description : `Execute ${this.currentPattern} pattern`}
                </p>
            `;
        } else {
            infoElement.innerHTML = `
                <p class="text-sm text-center" style="color: var(--text-tertiary);">
                    Select a pattern to see its description
                </p>
            `;
        }
    }

    updateTextStats() {
        const inputText = document.getElementById('inputText');
        if (!inputText) return;

        const text = inputText.value;
        const chars = text.length;
        const words = text.trim() ? text.trim().split(/\s+/).length : 0;

        const charCount = document.getElementById('charCount');
        const wordCount = document.getElementById('wordCount');

        if (charCount) charCount.textContent = chars.toLocaleString();
        if (wordCount) wordCount.textContent = words.toLocaleString();
    }

    updateExecuteButton() {
        const inputText = document.getElementById('inputText');
        const hasPattern = !!this.currentPattern;
        const hasInput = inputText && inputText.value.trim();
        const canExecute = hasPattern && hasInput && !this.isExecuting;

        console.log('updateExecuteButton called:', {
            currentPattern: this.currentPattern,
            hasPattern,
            hasInput,
            canExecute,
            isExecuting: this.isExecuting
        });

        this.ui.updateExecuteButton(this.isExecuting, canExecute);
    }

    async executePattern() {
        if (this.isExecuting) return;

        const inputText = document.getElementById('inputText')?.value?.trim();

        if (!this.currentPattern) {
            this.ui.showToast('Please select a pattern', 'warning');
            return;
        }

        if (!inputText) {
            this.ui.showToast('Please enter some text to process', 'warning');
            return;
        }

        // Validate input
        const validation = this.api.validateInput(inputText);
        if (!validation.isValid) {
            this.ui.showToast(validation.errors.join(', '), 'error');
            return;
        }

        // Show warnings
        validation.warnings.forEach(warning => {
            this.ui.showToast(warning, 'warning');
        });

        try {
            this.isExecuting = true;
            this.updateExecuteButton();

            // Show loading
            const loadingIndicator = document.getElementById('loadingIndicator');
            if (loadingIndicator) loadingIndicator.classList.remove('hidden');

            // Get execution options
            const options = this.getExecutionOptions();

            // Execute pattern
            let result;
            const isStreaming = document.getElementById('stream')?.checked;

            if (isStreaming) {
                const outputArea = document.getElementById('outputArea');
                if (outputArea) outputArea.innerHTML = '';

                result = await this.api.executePatternStream(
                    this.currentPattern,
                    inputText,
                    (chunk) => {
                        if (outputArea) {
                            outputArea.textContent += chunk;
                            outputArea.scrollTop = outputArea.scrollHeight;
                        }
                    },
                    options
                );
            } else {
                result = await this.api.executePattern(this.currentPattern, inputText, options);
                this.displayResult(result);
            }

            // Update status
            this.updateLastExecutionStatus(this.currentPattern);
            this.ui.enableOutputActions();
            this.ui.showToast('Pattern executed successfully', 'success');

        } catch (error) {
            console.error('Error executing pattern:', error);
            this.displayError(error.message);
            this.ui.showToast(`Execution failed: ${error.message}`, 'error');
        } finally {
            this.isExecuting = false;
            this.updateExecuteButton();

            const loadingIndicator = document.getElementById('loadingIndicator');
            if (loadingIndicator) loadingIndicator.classList.add('hidden');
        }
    }

    getExecutionOptions() {
        return {
            temperature: parseFloat(document.getElementById('temperature')?.value || 0.7),
            model: this.currentModel,
            stream: document.getElementById('stream')?.checked || false
        };
    }

    displayResult(result) {
        const outputArea = document.getElementById('outputArea');
        if (outputArea) {
            outputArea.textContent = result;
            outputArea.scrollTop = 0;
        }
    }

    displayError(errorMessage) {
        const outputArea = document.getElementById('outputArea');
        if (outputArea) {
            outputArea.innerHTML = `
                <div class="output-placeholder">
                    <i data-lucide="alert-triangle" style="color: var(--neon-secondary);"></i>
                    <p style="color: var(--neon-secondary); margin: var(--space-sm) 0;">Execution Error</p>
                    <p style="color: var(--text-secondary); font-size: 0.9rem;">${errorMessage}</p>
                </div>
            `;

            if (window.lucide) {
                window.lucide.createIcons();
            }
        }
    }

    updateLastExecutionStatus(pattern) {
        const statusElement = document.getElementById('lastExecution');
        if (statusElement) {
            const now = new Date();
            const timeString = now.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
            statusElement.textContent = `Last: ${pattern} at ${timeString}`;
        }
    }

    async copyOutput() {
        const outputArea = document.getElementById('outputArea');
        if (!outputArea) return;

        const text = outputArea.textContent;
        if (!text || text.trim() === '') {
            this.ui.showToast('No output to copy', 'warning');
            return;
        }

        try {
            if (navigator.clipboard) {
                await navigator.clipboard.writeText(text);
                this.ui.showToast('Output copied to clipboard', 'success');
            } else {
                this.ui.showToast('Clipboard access not available', 'warning');
            }
        } catch (error) {
            console.error('Error copying to clipboard:', error);
            this.ui.showToast('Failed to copy to clipboard', 'error');
        }
    }

    downloadOutput() {
        const outputArea = document.getElementById('outputArea');
        if (!outputArea) return;

        const text = outputArea.textContent;
        if (!text || text.trim() === '') {
            this.ui.showToast('No output to download', 'warning');
            return;
        }

        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        const filename = `fabric-output-${this.currentPattern}-${timestamp}.txt`;

        const blob = new Blob([text], { type: 'text/plain' });
        const url = URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = filename;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        URL.revokeObjectURL(url);

        this.ui.showToast('Output downloaded', 'success');
    }

    clearOutput() {
        const outputArea = document.getElementById('outputArea');
        if (outputArea) {
            outputArea.innerHTML = `
                <div class="output-placeholder">
                    <i data-lucide="cpu"></i>
                    <p>Output will appear here after executing a pattern</p>
                </div>
            `;

            if (window.lucide) {
                window.lucide.createIcons();
            }
        }

        // Disable output actions
        ['copyOutput', 'downloadOutput', 'clearOutput'].forEach(id => {
            const btn = document.getElementById(id);
            if (btn) btn.disabled = true;
        });
    }

    loadSettingsIntoForm() {
        // Load current settings into the form
        const settings = JSON.parse(localStorage.getItem('fabricApiSettings') || '{}');
        const providerKeys = JSON.parse(localStorage.getItem('providerApiKeys') || '{}');

        // API Configuration
        const apiUrlInput = document.getElementById('apiUrl');
        const apiKeyInput = document.getElementById('apiKey');
        if (apiUrlInput) apiUrlInput.value = this.api.baseUrl || 'http://localhost:8080';
        if (apiKeyInput) apiKeyInput.value = this.api.apiKey || '';

        // Provider API Keys
        const providerInputs = [
            'openaiKey', 'anthropicKey', 'googleKey', 'groqKey', 'ollamaUrl', 'xaiKey'
        ];
        providerInputs.forEach(inputId => {
            const input = document.getElementById(inputId);
            if (input) {
                const key = inputId.replace('Key', '').replace('Url', '');
                input.value = providerKeys[key] || '';
            }
        });

        // General Settings
        const timeoutInput = document.getElementById('timeout');
        const autoSaveInput = document.getElementById('autoSave');
        const streamingInput = document.getElementById('enableStreaming');

        if (timeoutInput) timeoutInput.value = (this.api.timeout / 1000) || 30;
        if (autoSaveInput) autoSaveInput.checked = settings.autoSave !== false;
        if (streamingInput) streamingInput.checked = settings.enableStreaming !== false;
    }

    saveSettings() {
        // Get form values
        const apiUrl = document.getElementById('apiUrl')?.value;
        const apiKey = document.getElementById('apiKey')?.value;
        const timeout = document.getElementById('timeout')?.value;
        const autoSave = document.getElementById('autoSave')?.checked;
        const enableStreaming = document.getElementById('enableStreaming')?.checked;

        // Get provider API keys
        const providerKeys = {
            openai: document.getElementById('openaiKey')?.value || '',
            anthropic: document.getElementById('anthropicKey')?.value || '',
            google: document.getElementById('googleKey')?.value || '',
            groq: document.getElementById('groqKey')?.value || '',
            ollama: document.getElementById('ollamaUrl')?.value || '',
            xai: document.getElementById('xaiKey')?.value || ''
        };

        // Prepare settings
        const settings = {
            baseUrl: apiUrl || 'http://localhost:8080',
            apiKey: apiKey || null,
            timeout: (parseInt(timeout) || 30) * 1000,
            autoSave: autoSave !== false,
            enableStreaming: enableStreaming !== false
        };

        try {
            // Save main settings
            if (this.api.saveSettings(settings)) {
                // Save provider keys separately for security
                localStorage.setItem('providerApiKeys', JSON.stringify(providerKeys));

                // Update streaming default
                const streamCheckbox = document.getElementById('stream');
                if (streamCheckbox && enableStreaming) {
                    streamCheckbox.checked = true;
                }

                document.getElementById('settingsModal')?.classList.add('hidden');
                this.ui.showToast('Settings saved successfully', 'success');

                // Test new connection
                this.api.testConnection().then(connected => {
                    this.ui.updateConnectionStatus(connected);

                    if (connected) {
                        this.ui.showToast('Connection test successful', 'success');
                        // Reload models to include provider-specific ones
                        this.loadModels(true);
                    } else {
                        this.ui.showToast('Connection test failed - running in demo mode', 'warning');
                    }
                });
            } else {
                this.ui.showToast('Error saving settings', 'error');
            }
        } catch (error) {
            console.error('Error saving settings:', error);
            this.ui.showToast('Error saving settings: ' + error.message, 'error');
        }
    }
}

// ============================================
// INITIALIZE APPLICATION
// ============================================

// Wait for DOM to be ready
console.log('Document ready state:', document.readyState);
if (document.readyState === 'loading') {
    console.log('Waiting for DOMContentLoaded...');
    document.addEventListener('DOMContentLoaded', () => {
        console.log('DOMContentLoaded fired, creating app...');
        window.fabricApp = new EnhancedFabricApp();
    });
} else {
    console.log('DOM already ready, creating app immediately...');
    window.fabricApp = new EnhancedFabricApp();
}

console.log('Enhanced Fabric API loaded successfully!');
