<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Fabric GUI Comprehensive Test</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; max-width: 1200px; margin: 0 auto; }
        .test-grid { display: grid; grid-template-columns: 1fr 1fr; gap: 20px; }
        .test-section { padding: 15px; border: 1px solid #ccc; border-radius: 8px; }
        .test-result { margin: 10px 0; padding: 8px; border-radius: 4px; }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        button { margin: 5px; padding: 8px 16px; cursor: pointer; }
        .test-button { background: #007bff; color: white; border: none; border-radius: 4px; }
        .test-button:hover { background: #0056b3; }
        #testResults { max-height: 400px; overflow-y: auto; }
        .test-item { margin: 5px 0; padding: 5px; background: #f8f9fa; border-radius: 3px; }
    </style>
</head>
<body>
    <h1>Fabric GUI Comprehensive Test Suite</h1>
    
    <div class="test-grid">
        <div class="test-section">
            <h2>Core Functionality Tests</h2>
            <button class="test-button" onclick="testPatternLoading()">Test Pattern Loading</button>
            <button class="test-button" onclick="testConnectionStatus()">Test Connection Status</button>
            <button class="test-button" onclick="testExecuteButton()">Test Execute Button</button>
            <button class="test-button" onclick="testPatternExecution()">Test Pattern Execution</button>
            <button class="test-button" onclick="testTextStats()">Test Text Statistics</button>
        </div>
        
        <div class="test-section">
            <h2>UI Component Tests</h2>
            <button class="test-button" onclick="testThemeToggle()">Test Theme Toggle</button>
            <button class="test-button" onclick="testSettingsModal()">Test Settings Modal</button>
            <button class="test-button" onclick="testAdvancedOptions()">Test Advanced Options</button>
            <button class="test-button" onclick="testOutputActions()">Test Output Actions</button>
            <button class="test-button" onclick="testToastNotifications()">Test Toast Notifications</button>
        </div>
    </div>
    
    <div class="test-section" style="margin-top: 20px;">
        <h2>Test Results</h2>
        <button class="test-button" onclick="runAllTests()">Run All Tests</button>
        <button class="test-button" onclick="clearResults()">Clear Results</button>
        <div id="testResults"></div>
    </div>

    <script src="fabric-api-enhanced.js"></script>
    <script>
        let testResults = [];
        
        function addTestResult(testName, status, message) {
            const result = {
                test: testName,
                status: status,
                message: message,
                timestamp: new Date().toLocaleTimeString()
            };
            testResults.push(result);
            updateResultsDisplay();
        }
        
        function updateResultsDisplay() {
            const container = document.getElementById('testResults');
            container.innerHTML = testResults.map(result => 
                `<div class="test-item ${result.status}">
                    <strong>[${result.timestamp}] ${result.test}:</strong> ${result.message}
                </div>`
            ).join('');
            container.scrollTop = container.scrollHeight;
        }
        
        function clearResults() {
            testResults = [];
            updateResultsDisplay();
        }
        
        // Test Functions
        async function testPatternLoading() {
            try {
                if (!window.fabricApp) {
                    addTestResult('Pattern Loading', 'error', 'Fabric app not initialized');
                    return;
                }
                
                const patterns = await window.fabricApp.api.getPatterns();
                if (patterns && patterns.length > 0) {
                    addTestResult('Pattern Loading', 'success', `Loaded ${patterns.length} patterns`);
                    
                    // Test pattern dropdown population
                    const select = document.querySelector('#patternSelect');
                    if (select && select.options.length > 1) {
                        addTestResult('Pattern Dropdown', 'success', `Dropdown has ${select.options.length} options`);
                    } else {
                        addTestResult('Pattern Dropdown', 'error', 'Pattern dropdown not populated');
                    }
                } else {
                    addTestResult('Pattern Loading', 'error', 'No patterns loaded');
                }
            } catch (error) {
                addTestResult('Pattern Loading', 'error', error.message);
            }
        }
        
        async function testConnectionStatus() {
            try {
                if (!window.fabricApp) {
                    addTestResult('Connection Status', 'error', 'Fabric app not initialized');
                    return;
                }
                
                const isConnected = await window.fabricApp.api.testConnection();
                const statusElement = document.querySelector('#connectionStatus');
                
                if (statusElement) {
                    const statusText = statusElement.textContent;
                    addTestResult('Connection Status', 'info', `Status: ${statusText} (Connected: ${isConnected})`);
                } else {
                    addTestResult('Connection Status', 'error', 'Status element not found');
                }
            } catch (error) {
                addTestResult('Connection Status', 'error', error.message);
            }
        }
        
        function testExecuteButton() {
            try {
                const executeBtn = document.querySelector('#executeBtn');
                if (!executeBtn) {
                    addTestResult('Execute Button', 'error', 'Execute button not found');
                    return;
                }
                
                const initialState = executeBtn.disabled;
                addTestResult('Execute Button', 'info', `Initial state: ${initialState ? 'disabled' : 'enabled'}`);
                
                // Test with pattern selection
                const patternSelect = document.querySelector('#patternSelect');
                if (patternSelect && patternSelect.options.length > 1) {
                    patternSelect.value = patternSelect.options[1].value;
                    patternSelect.dispatchEvent(new Event('change'));
                    
                    setTimeout(() => {
                        const newState = executeBtn.disabled;
                        addTestResult('Execute Button', newState ? 'warning' : 'success', 
                            `After pattern selection: ${newState ? 'disabled' : 'enabled'}`);
                    }, 100);
                } else {
                    addTestResult('Execute Button', 'warning', 'Cannot test pattern selection - no patterns available');
                }
            } catch (error) {
                addTestResult('Execute Button', 'error', error.message);
            }
        }
        
        async function testPatternExecution() {
            try {
                if (!window.fabricApp) {
                    addTestResult('Pattern Execution', 'error', 'Fabric app not initialized');
                    return;
                }
                
                // Set up test conditions
                const patternSelect = document.querySelector('#patternSelect');
                const inputText = document.querySelector('#inputText');
                
                if (!patternSelect || !inputText) {
                    addTestResult('Pattern Execution', 'error', 'Required elements not found');
                    return;
                }
                
                // Select first pattern and add test text
                if (patternSelect.options.length > 1) {
                    patternSelect.value = patternSelect.options[1].value;
                    patternSelect.dispatchEvent(new Event('change'));
                }
                
                inputText.value = 'This is a test input for pattern execution.';
                inputText.dispatchEvent(new Event('input'));
                
                // Wait a moment for UI updates
                setTimeout(async () => {
                    try {
                        await window.fabricApp.executePattern();
                        addTestResult('Pattern Execution', 'success', 'Pattern execution completed');
                    } catch (error) {
                        addTestResult('Pattern Execution', 'error', `Execution failed: ${error.message}`);
                    }
                }, 200);
                
            } catch (error) {
                addTestResult('Pattern Execution', 'error', error.message);
            }
        }
        
        function testTextStats() {
            try {
                const inputText = document.querySelector('#inputText');
                const charCount = document.querySelector('#charCount');
                const wordCount = document.querySelector('#wordCount');
                
                if (!inputText || !charCount || !wordCount) {
                    addTestResult('Text Stats', 'error', 'Required elements not found');
                    return;
                }
                
                const testText = 'This is a test with exactly seven words.';
                inputText.value = testText;
                inputText.dispatchEvent(new Event('input'));
                
                setTimeout(() => {
                    const chars = charCount.textContent;
                    const words = wordCount.textContent;
                    addTestResult('Text Stats', 'success', `Characters: ${chars}, Words: ${words}`);
                }, 100);
                
            } catch (error) {
                addTestResult('Text Stats', 'error', error.message);
            }
        }
        
        function testThemeToggle() {
            try {
                const themeToggle = document.querySelector('#themeToggle');
                const body = document.body;
                
                if (!themeToggle) {
                    addTestResult('Theme Toggle', 'error', 'Theme toggle button not found');
                    return;
                }
                
                const initialTheme = body.className;
                themeToggle.click();
                
                setTimeout(() => {
                    const newTheme = body.className;
                    if (newTheme !== initialTheme) {
                        addTestResult('Theme Toggle', 'success', `Theme changed from ${initialTheme} to ${newTheme}`);
                    } else {
                        addTestResult('Theme Toggle', 'error', 'Theme did not change');
                    }
                }, 100);
                
            } catch (error) {
                addTestResult('Theme Toggle', 'error', error.message);
            }
        }
        
        function testSettingsModal() {
            try {
                const settingsBtn = document.querySelector('#settingsBtn');
                const settingsModal = document.querySelector('#settingsModal');
                
                if (!settingsBtn || !settingsModal) {
                    addTestResult('Settings Modal', 'error', 'Settings elements not found');
                    return;
                }
                
                settingsBtn.click();
                
                setTimeout(() => {
                    const isVisible = !settingsModal.classList.contains('hidden');
                    if (isVisible) {
                        addTestResult('Settings Modal', 'success', 'Settings modal opened successfully');
                        
                        // Close it
                        const closeBtn = document.querySelector('#closeSettings');
                        if (closeBtn) closeBtn.click();
                    } else {
                        addTestResult('Settings Modal', 'error', 'Settings modal did not open');
                    }
                }, 100);
                
            } catch (error) {
                addTestResult('Settings Modal', 'error', error.message);
            }
        }
        
        function testAdvancedOptions() {
            try {
                const advancedToggle = document.querySelector('#advancedToggle');
                const advancedPanel = document.querySelector('#advancedPanel');
                
                if (!advancedToggle || !advancedPanel) {
                    addTestResult('Advanced Options', 'error', 'Advanced options elements not found');
                    return;
                }
                
                const initialState = advancedPanel.classList.contains('hidden');
                advancedToggle.click();
                
                setTimeout(() => {
                    const newState = advancedPanel.classList.contains('hidden');
                    if (newState !== initialState) {
                        addTestResult('Advanced Options', 'success', `Panel toggled from ${initialState ? 'hidden' : 'visible'} to ${newState ? 'hidden' : 'visible'}`);
                    } else {
                        addTestResult('Advanced Options', 'error', 'Advanced options panel did not toggle');
                    }
                }, 100);
                
            } catch (error) {
                addTestResult('Advanced Options', 'error', error.message);
            }
        }
        
        function testOutputActions() {
            try {
                const copyBtn = document.querySelector('#copyOutput');
                const downloadBtn = document.querySelector('#downloadOutput');
                const clearBtn = document.querySelector('#clearOutput');
                
                if (!copyBtn || !downloadBtn || !clearBtn) {
                    addTestResult('Output Actions', 'error', 'Output action buttons not found');
                    return;
                }
                
                addTestResult('Output Actions', 'success', 'All output action buttons found');
                
                // Test clear functionality
                const outputArea = document.querySelector('#outputArea');
                if (outputArea) {
                    outputArea.textContent = 'Test output content';
                    clearBtn.click();
                    
                    setTimeout(() => {
                        if (outputArea.textContent.trim() === '' || outputArea.innerHTML.includes('output-placeholder')) {
                            addTestResult('Clear Output', 'success', 'Output cleared successfully');
                        } else {
                            addTestResult('Clear Output', 'warning', 'Output may not have cleared completely');
                        }
                    }, 100);
                }
                
            } catch (error) {
                addTestResult('Output Actions', 'error', error.message);
            }
        }
        
        function testToastNotifications() {
            try {
                if (!window.fabricApp || !window.fabricApp.ui) {
                    addTestResult('Toast Notifications', 'error', 'UI manager not available');
                    return;
                }
                
                // Test different toast types
                window.fabricApp.ui.showToast('Test success message', 'success', 1000);
                window.fabricApp.ui.showToast('Test error message', 'error', 1000);
                window.fabricApp.ui.showToast('Test warning message', 'warning', 1000);
                window.fabricApp.ui.showToast('Test info message', 'info', 1000);
                
                addTestResult('Toast Notifications', 'success', 'Toast notifications triggered');
                
            } catch (error) {
                addTestResult('Toast Notifications', 'error', error.message);
            }
        }
        
        async function runAllTests() {
            clearResults();
            addTestResult('Test Suite', 'info', 'Starting comprehensive test suite...');
            
            const tests = [
                testPatternLoading,
                testConnectionStatus,
                testExecuteButton,
                testTextStats,
                testThemeToggle,
                testSettingsModal,
                testAdvancedOptions,
                testOutputActions,
                testToastNotifications
            ];
            
            for (let i = 0; i < tests.length; i++) {
                await new Promise(resolve => {
                    setTimeout(async () => {
                        await tests[i]();
                        resolve();
                    }, i * 500);
                });
            }
            
            // Test pattern execution last (takes longer)
            setTimeout(() => {
                testPatternExecution();
                addTestResult('Test Suite', 'info', 'All tests completed');
            }, tests.length * 500);
        }
        
        // Initialize when page loads
        window.addEventListener('load', () => {
            addTestResult('Initialization', 'info', 'Test page loaded successfully');
        });
    </script>
</body>
</html>
