<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Fabric GUI Test</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
    </style>
</head>
<body>
    <h1>Fabric GUI Test Page</h1>
    
    <div class="test-section">
        <h2>Pattern Loading Test</h2>
        <div id="patternTest">Testing...</div>
        <select id="testPatternSelect">
            <option value="">Loading...</option>
        </select>
    </div>
    
    <div class="test-section">
        <h2>API Connection Test</h2>
        <div id="connectionTest">Testing...</div>
    </div>
    
    <div class="test-section">
        <h2>Console Output</h2>
        <div id="consoleOutput" style="background: #f0f0f0; padding: 10px; font-family: monospace; white-space: pre-wrap;"></div>
    </div>

    <script src="fabric-api-enhanced.js"></script>
    <script>
        // Capture console output
        const originalLog = console.log;
        const originalError = console.error;
        const originalWarn = console.warn;
        const consoleOutput = document.getElementById('consoleOutput');
        
        function addToConsole(type, ...args) {
            const message = args.map(arg => 
                typeof arg === 'object' ? JSON.stringify(arg, null, 2) : String(arg)
            ).join(' ');
            consoleOutput.textContent += `[${type.toUpperCase()}] ${message}\n`;
            consoleOutput.scrollTop = consoleOutput.scrollHeight;
        }
        
        console.log = (...args) => {
            originalLog(...args);
            addToConsole('log', ...args);
        };
        
        console.error = (...args) => {
            originalError(...args);
            addToConsole('error', ...args);
        };
        
        console.warn = (...args) => {
            originalWarn(...args);
            addToConsole('warn', ...args);
        };
        
        // Test the API directly
        async function runTests() {
            console.log('Starting tests...');
            
            try {
                // Test API creation
                const api = new EnhancedFabricAPI();
                console.log('API instance created');
                
                // Test connection
                const connectionTest = document.getElementById('connectionTest');
                try {
                    const isConnected = await api.testConnection();
                    connectionTest.innerHTML = `<span class="${isConnected ? 'success' : 'error'}">
                        Connection: ${isConnected ? 'SUCCESS' : 'FAILED'}
                    </span>`;
                } catch (error) {
                    connectionTest.innerHTML = `<span class="error">Connection Error: ${error.message}</span>`;
                }
                
                // Test pattern loading
                const patternTest = document.getElementById('patternTest');
                try {
                    const patterns = await api.getPatterns();
                    console.log('Patterns loaded:', patterns);
                    
                    patternTest.innerHTML = `<span class="success">
                        Loaded ${patterns.length} patterns
                    </span>`;
                    
                    // Populate test dropdown
                    const select = document.getElementById('testPatternSelect');
                    select.innerHTML = '<option value="">Select a pattern...</option>';
                    patterns.forEach(pattern => {
                        const option = document.createElement('option');
                        option.value = pattern.name;
                        option.textContent = pattern.name;
                        select.appendChild(option);
                    });
                    
                } catch (error) {
                    patternTest.innerHTML = `<span class="error">Pattern Error: ${error.message}</span>`;
                }
                
            } catch (error) {
                console.error('Test failed:', error);
            }
        }
        
        // Run tests when page loads
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', runTests);
        } else {
            runTests();
        }
    </script>
</body>
</html>
