{"name": "strip-final-newline", "version": "2.0.0", "description": "Strip the final newline character from a string/buffer", "license": "MIT", "repository": "sindresorhus/strip-final-newline", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=6"}, "scripts": {"test": "xo && ava"}, "files": ["index.js"], "keywords": ["strip", "trim", "remove", "delete", "final", "last", "end", "file", "newline", "linebreak", "character", "string", "buffer"], "devDependencies": {"ava": "^0.25.0", "xo": "^0.23.0"}}