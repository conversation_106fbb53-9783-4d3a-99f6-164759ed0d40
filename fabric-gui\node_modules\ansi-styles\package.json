{"name": "ansi-styles", "version": "4.3.0", "description": "ANSI escape codes for styling strings in the terminal", "license": "MIT", "repository": "chalk/ansi-styles", "funding": "https://github.com/chalk/ansi-styles?sponsor=1", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=8"}, "scripts": {"test": "xo && ava && tsd", "screenshot": "svg-term --command='node screenshot' --out=screenshot.svg --padding=3 --width=55 --height=3 --at=1000 --no-cursor"}, "files": ["index.js", "index.d.ts"], "keywords": ["ansi", "styles", "color", "colour", "colors", "terminal", "console", "cli", "string", "tty", "escape", "formatting", "rgb", "256", "shell", "xterm", "log", "logging", "command-line", "text"], "dependencies": {"color-convert": "^2.0.1"}, "devDependencies": {"@types/color-convert": "^1.9.0", "ava": "^2.3.0", "svg-term-cli": "^2.1.1", "tsd": "^0.11.0", "xo": "^0.25.3"}}